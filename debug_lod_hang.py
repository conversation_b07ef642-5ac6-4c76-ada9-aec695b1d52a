#!/usr/bin/env python3
"""
调试LOD更新卡住的问题
快速定位代码卡在哪个步骤
"""

import omni
from omni import usd
import time
from pxr import Gf, UsdGeom

# 导入LOD调度器
from lod.lod_scheduler import HierarchicalTilesetLODManager
from lod.lod_config import LODConfig

def debug_lod_hang():
    """调试LOD更新卡住的问题"""
    print("🔍 Starting LOD Hang Debug...")
    
    stage = usd.get_context().get_stage()
    camera_path = "/World/Camera"
    
    # 创建LOD配置
    lod_config = LODConfig()
    
    # 创建LOD管理器
    lod_manager = HierarchicalTilesetLODManager(
        stage=stage,
        camera_path=camera_path,
        config=lod_config
    )
    
    print("✅ LOD Manager created")
    
    # 测试每个步骤
    try:
        print("\n🔄 Step 1: Testing collect_all_tiles()...")
        all_tiles = lod_manager.collect_all_tiles()
        print(f"✅ Collected {len(all_tiles)} tiles")
        
        print("\n🔄 Step 2: Testing get_tileset_region_bounds()...")
        region_bounds = lod_manager.get_tileset_region_bounds()
        if region_bounds:
            print(f"✅ Got region bounds: center={region_bounds.center}")
        else:
            print("❌ Failed to get region bounds")
            return
            
        print("\n🔄 Step 3: Testing camera access...")
        camera = stage.GetPrimAtPath(camera_path)
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())
            print(f"✅ Camera position: {camera_position}")
        else:
            print("❌ Camera not found")
            return
            
        print("\n🔄 Step 4: Testing LOD scheduler...")
        if lod_manager.lod_scheduler:
            print("✅ LOD scheduler exists")
            
            # 测试LOD选择
            print("🔄 Testing LOD selection...")
            selected_lod, lod_info = lod_manager.lod_scheduler.select_lod_by_sse_and_distance(
                region_bounds, camera_position, verbose=True
            )
            print(f"✅ LOD selection works: {selected_lod}")
        else:
            print("❌ LOD scheduler not found")
            return
            
        print("\n🔄 Step 5: Testing tile content processing...")
        content_count = 0
        for i, tile in enumerate(all_tiles):
            if i % 5 == 0:
                print(f"   Processing tile {i+1}/{len(all_tiles)}: {tile['name']}")
            
            if tile['content_nodes']:
                content_count += len(tile['content_nodes'])
                
        print(f"✅ Found {content_count} content nodes total")
        
        print("\n🔄 Step 6: Testing frustum culling...")
        from tileset_utils import FrustumCuller
        frustum_planes, _, _ = FrustumCuller.extract_camera_frustum_planes(camera)
        if frustum_planes:
            print("✅ Frustum culling available")
        else:
            print("⚠️  Frustum culling not available")
            
        print("\n🔄 Step 7: Testing simple LOD update...")
        start_time = time.time()

        # 先尝试简化版本的LOD更新
        print("🔄 Starting simple LOD update...")
        result = lod_manager.update_tileset_lod_visibility_simple(verbose=True)
        elapsed = time.time() - start_time

        if result[0] is not None:
            print(f"✅ Simple LOD update completed in {elapsed:.2f}s")
            print(f"   Result: {result}")
        else:
            print(f"❌ Simple LOD update failed after {elapsed:.2f}s")
            return

        print("\n🔄 Step 8: Testing full update with timeout...")
        start_time = time.time()
        timeout = 15.0  # 15秒超时

        # 尝试完整的LOD更新
        print("🔄 Starting full LOD update...")
        try:
            result = lod_manager.update_tileset_lod_visibility(verbose=True, timeout=timeout)
            elapsed = time.time() - start_time

            if result[0] is not None:
                print(f"✅ Full LOD update completed in {elapsed:.2f}s")
                print(f"   Result: {result}")
            else:
                print(f"❌ Full LOD update failed after {elapsed:.2f}s")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ Full LOD update crashed after {elapsed:.2f}s: {e}")
            
    except Exception as e:
        print(f"❌ ERROR in debug: {e}")
        import traceback
        traceback.print_exc()

def debug_specific_tile():
    """调试特定Tile的处理"""
    print("\n🔍 Debugging specific tile processing...")
    
    stage = usd.get_context().get_stage()
    
    # 查找第一个有内容的tile
    from lod.lod_scheduler import HierarchicalTilesetLODManager
    lod_config = LODConfig()
    lod_manager = HierarchicalTilesetLODManager(stage, "/World/Camera", lod_config)
    
    all_tiles = lod_manager.collect_all_tiles()
    
    for tile in all_tiles:
        if tile['content_nodes']:
            print(f"🔍 Debugging tile: {tile['name']}")
            print(f"   Content nodes: {len(tile['content_nodes'])}")
            print(f"   Bounds: {tile.get('bounds')}")
            
            # 测试每个content node
            for i, content in enumerate(tile['content_nodes']):
                print(f"   Content {i+1}: {content['name']}")
                print(f"     LOD level: {content.get('lod_level', 'Unknown')}")
                print(f"     Prim path: {content['prim'].GetPath()}")
                
                # 测试可见性操作
                try:
                    imageable = UsdGeom.Imageable(content['prim'])
                    current_vis = imageable.ComputeVisibility()
                    print(f"     Current visibility: {current_vis}")
                except Exception as e:
                    print(f"     ❌ Error accessing visibility: {e}")
            
            break  # 只测试第一个有内容的tile

def main():
    """主函数"""
    print("LOD卡住问题调试工具")
    print("=" * 50)
    
    choice = input("选择调试模式:\n1. 完整调试\n2. 特定Tile调试\n请输入 (1/2): ")
    
    if choice == "1":
        debug_lod_hang()
    elif choice == "2":
        debug_specific_tile()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
