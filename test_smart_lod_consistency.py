#!/usr/bin/env python3
"""
测试简化的LOD策略
验证相机在LOD 20 Tile内时的优先加载逻辑
"""

import omni
from omni import usd
import time
from pxr import Gf, UsdGeom

# 导入LOD调度器
from lod.lod_scheduler import HierarchicalTilesetLODManager
from lod.lod_config import LODConfig

class SimplifiedLODTest:
    def __init__(self):
        self.stage = usd.get_context().get_stage()
        self.camera_path = "/World/Camera"
        
        # 创建LOD配置
        self.lod_config = LODConfig()
        
        # 创建LOD管理器
        self.lod_manager = HierarchicalTilesetLODManager(
            stage=self.stage,
            camera_path=self.camera_path,
            config=self.lod_config
        )
        
        # 测试位置 - 特别设计来触发各种情况
        self.test_positions = [
            # 相机在Tile内的位置 - 应该强制使用最高LOD
            Gf.Vec3f(50, 50, 10),   # 很可能在某个Tile内
            Gf.Vec3f(100, 100, 15), # 另一个可能在Tile内的位置
            # 边界位置 - 可能触发LOD 18和20混合
            Gf.Vec3f(200, 200, 30),
            Gf.Vec3f(150, 150, 25),
            # 中等距离边界
            Gf.Vec3f(300, 300, 40),
            # 远距离 - 应该统一选择Low LOD
            Gf.Vec3f(800, 800, 80),
        ]
        
    def set_camera_position(self, position: Gf.Vec3f):
        """设置相机位置"""
        camera_prim = self.stage.GetPrimAtPath(self.camera_path)
        if camera_prim:
            xformable = UsdGeom.Xformable(camera_prim)
            # 创建变换矩阵
            transform = Gf.Matrix4d()
            transform.SetTranslate(Gf.Vec3d(position))
            xformable.SetLocalTransformation(transform)
            print(f"📍 Camera moved to: ({position[0]:.1f}, {position[1]:.1f}, {position[2]:.1f})")
        else:
            print("❌ Camera not found!")
            
    def check_camera_inside_tiles(self, camera_position):
        """检查相机是否在任何Tile内"""
        camera_inside_tiles = []
        all_tiles = self.lod_manager.collect_all_tiles()

        for tile in all_tiles:
            tile_bounds = tile.get('bounds')
            if tile_bounds and self._is_camera_inside_tile(camera_position, tile_bounds):
                camera_inside_tiles.append(tile['name'])

        return camera_inside_tiles

    def _is_camera_inside_tile(self, camera_position, tile_bounds):
        """检测相机是否在Tile包围盒内"""
        if not tile_bounds:
            return False

        min_pos = tile_bounds.min
        max_pos = tile_bounds.max

        return (min_pos[0] <= camera_position[0] <= max_pos[0] and
                min_pos[1] <= camera_position[1] <= max_pos[1] and
                min_pos[2] <= camera_position[2] <= max_pos[2])

    def analyze_lod_consistency(self, camera_position):
        """分析LOD一致性"""
        print(f"\n🔍 Analyzing LOD Consistency...")

        # 检查相机是否在Tile内
        camera_inside_tiles = self.check_camera_inside_tiles(camera_position)
        if camera_inside_tiles:
            print(f"   📍 Camera is inside tiles: {camera_inside_tiles}")

        # 收集所有可见的内容节点及其LOD级别
        visible_lods = {}  # {lod_level: count}
        visible_contents = []
        highest_lod_in_camera_tiles = None

        all_tiles = self.lod_manager.collect_all_tiles()

        for tile in all_tiles:
            tile_is_camera_inside = tile['name'] in camera_inside_tiles

            for content in tile.get('content_nodes', []):
                imageable = UsdGeom.Imageable(content['prim'])
                visibility = imageable.ComputeVisibility()

                if visibility == UsdGeom.Tokens.inherited:
                    lod_level = content.get('lod_level', 14)
                    visible_lods[lod_level] = visible_lods.get(lod_level, 0) + 1
                    visible_contents.append({
                        'name': content['name'],
                        'lod_level': lod_level,
                        'tile_name': tile['name'],
                        'camera_inside': tile_is_camera_inside
                    })

                    # 记录相机所在Tile的最高LOD
                    if tile_is_camera_inside:
                        if highest_lod_in_camera_tiles is None or lod_level > highest_lod_in_camera_tiles:
                            highest_lod_in_camera_tiles = lod_level

        # 分析结果
        active_lod_levels = list(visible_lods.keys())
        is_consistent = len(active_lod_levels) <= 1

        print(f"   Visible LOD levels: {sorted(active_lod_levels)}")
        print(f"   LOD distribution: {visible_lods}")
        print(f"   Total visible contents: {sum(visible_lods.values())}")

        if camera_inside_tiles and highest_lod_in_camera_tiles:
            print(f"   📍 Highest LOD in camera tiles: {highest_lod_in_camera_tiles}")

        if is_consistent:
            print("   ✅ CONSISTENT: Only one LOD level is visible")
        else:
            print("   ⚠️  MIXED LOD: Multiple LOD levels are visible")
            print("   📋 Detailed breakdown:")
            for content in visible_contents:
                camera_flag = " 📍" if content['camera_inside'] else ""
                print(f"      - {content['name']} (LOD {content['lod_level']}) in {content['tile_name']}{camera_flag}")

        return is_consistent, visible_lods, visible_contents, camera_inside_tiles
        
    def run_consistency_test(self):
        """运行一致性测试"""
        print("🚀 Starting Smart LOD Consistency Test")
        print("=" * 70)
        
        results = []
        
        for i, position in enumerate(self.test_positions):
            print(f"\n🎯 Test {i+1}/6: Testing position {position}")
            print("-" * 50)

            # 设置相机位置
            self.set_camera_position(position)
            time.sleep(0.5)  # 等待场景更新

            # 更新LOD可见性
            selected_lod, distance, sse = self.lod_manager.update_tileset_lod_visibility(verbose=True)

            # 分析一致性（包括相机在Tile内的检测）
            is_consistent, visible_lods, visible_contents, camera_inside_tiles = self.analyze_lod_consistency(position)

            # 记录结果
            test_result = {
                'position': position,
                'selected_lod': selected_lod,
                'distance': distance,
                'sse': sse,
                'is_consistent': is_consistent,
                'visible_lods': visible_lods,
                'visible_count': len(visible_contents),
                'camera_inside_tiles': camera_inside_tiles
            }
            results.append(test_result)

            print(f"\n📊 Test {i+1} Summary:")
            print(f"   Position: ({position[0]:.1f}, {position[1]:.1f}, {position[2]:.1f})")
            print(f"   Selected LOD: {selected_lod}")
            print(f"   Distance: {distance:.1f}m")
            print(f"   Camera Inside Tiles: {len(camera_inside_tiles)} tiles")
            if camera_inside_tiles:
                print(f"   Inside Tile Names: {camera_inside_tiles}")
            print(f"   Consistency: {'✅ PASS' if is_consistent else '❌ FAIL'}")
            print(f"   Visible Contents: {len(visible_contents)}")

            print("\n" + "="*70)
            time.sleep(1)  # 观察间隔
            
        # 总结测试结果
        self.summarize_test_results(results)
        
    def summarize_test_results(self, results):
        """总结测试结果"""
        print(f"\n🎯 Test Results Summary")
        print("=" * 70)
        
        consistent_count = sum(1 for r in results if r['is_consistent'])
        total_tests = len(results)
        
        print(f"📊 Overall Statistics:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Consistent Results: {consistent_count}")
        print(f"   Mixed LOD Results: {total_tests - consistent_count}")
        print(f"   Success Rate: {consistent_count/total_tests*100:.1f}%")
        
        print(f"\n📋 Detailed Results:")
        camera_inside_tests = 0
        for i, result in enumerate(results):
            status = "✅ PASS" if result['is_consistent'] else "❌ FAIL"
            camera_inside_count = len(result.get('camera_inside_tiles', []))
            if camera_inside_count > 0:
                camera_inside_tests += 1
            camera_flag = f" 📍Inside:{camera_inside_count}" if camera_inside_count > 0 else ""
            print(f"   Test {i+1}: {status} - LOD {result['selected_lod']} - "
                  f"Distance {result['distance']:.1f}m - "
                  f"Visible: {result['visible_count']}{camera_flag}")

        # 性能评估
        avg_visible = sum(r['visible_count'] for r in results) / len(results)
        print(f"\n⚡ Performance Metrics:")
        print(f"   Average Visible Contents: {avg_visible:.1f}")
        print(f"   Camera Inside Tile Tests: {camera_inside_tests}/{total_tests}")
        print(f"   Strategy: Smart Consistency + Camera Inside Rules")

        if consistent_count == total_tests:
            print(f"\n🎉 SUCCESS: All tests passed! Mixed LOD issue resolved.")
            if camera_inside_tests > 0:
                print(f"   📍 Camera inside tile logic working correctly!")
        elif consistent_count >= total_tests * 0.8:
            print(f"\n✅ GOOD: Most tests passed. Minor mixed LOD cases may exist.")
        else:
            print(f"\n⚠️  NEEDS IMPROVEMENT: Mixed LOD issue still exists.")

def main():
    """主函数"""
    test = SimplifiedLODTest()

    print("简化LOD策略测试")
    print("这个测试将验证相机在LOD 20 Tile内时的优先加载逻辑")
    print("确保只加载LOD 20而不是父级LOD")
    
    input("按Enter键开始测试...")
    
    test.run_consistency_test()

if __name__ == "__main__":
    main()
