from pxr import Usd, UsdGeom, Gf
import omni.usd
import math
import json
import os
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class LODLevel(Enum):
    """LOD级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class BoundingBox:
    """边界框数据结构"""
    min_point: Gf.Vec3f
    max_point: Gf.Vec3f
    
    @property
    def center(self) -> Gf.Vec3f:
        """计算边界框中心点"""
        return (self.min_point + self.max_point) * 0.5
    
    @property
    def size(self) -> Gf.Vec3f:
        """计算边界框尺寸"""
        return self.max_point - self.min_point
    
    @property
    def area(self) -> float:
        """计算边界框表面积"""
        size = self.size
        return 2 * (size[0] * size[1] + size[1] * size[2] + size[0] * size[2])
    
    @property
    def volume(self) -> float:
        """计算边界框体积"""
        size = self.size
        return size[0] * size[1] * size[2]
    
    def contains_point(self, point: Gf.Vec3f) -> bool:
        """检查点是否在边界框内"""
        return (self.min_point[0] <= point[0] <= self.max_point[0] and
                self.min_point[1] <= point[1] <= self.max_point[1] and
                self.min_point[2] <= point[2] <= self.max_point[2])
    
    def intersects(self, other: 'BoundingBox') -> bool:
        """检查两个边界框是否相交"""
        return not (self.max_point[0] < other.min_point[0] or
                   self.min_point[0] > other.max_point[0] or
                   self.max_point[1] < other.min_point[1] or
                   self.min_point[1] > other.max_point[1] or
                   self.max_point[2] < other.min_point[2] or
                   self.min_point[2] > other.max_point[2])

@dataclass
class LODTile:
    """LOD瓦片数据结构"""
    id: str
    bounding_box: BoundingBox
    lod_level: LODLevel
    usdz_path: str
    screen_error: float  # 屏幕误差阈值
    geometric_error: float  # 几何误差（从tileset.json中获取）
    tile_index: int = 0  # tile在同一depth层级内的索引
    depth: int = 0  # tile的深度层级
    children: List['LODTile'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

class OctreeNode:
    """八叉树节点"""
    def __init__(self, bounding_box: BoundingBox, max_depth: int = 8, min_area_threshold: float = 100.0):
        self.bounding_box = bounding_box
        self.max_depth = max_depth
        self.min_area_threshold = min_area_threshold
        self.children: List[Optional[OctreeNode]] = [None] * 8
        self.lod_tiles: List[LODTile] = []
        self.depth = 0
    
    def subdivide(self, current_depth: int = 0):
        """细分八叉树节点"""
        if current_depth >= self.max_depth or self.bounding_box.area <= self.min_area_threshold:
            return
        
        center = self.bounding_box.center
        min_point = self.bounding_box.min_point
        max_point = self.bounding_box.max_point
        
        # 创建8个子节点
        child_boxes = [
            BoundingBox(min_point, center),  # 000
            BoundingBox(Gf.Vec3f(center[0], min_point[1], min_point[2]), 
                       Gf.Vec3f(max_point[0], center[1], center[2])),  # 100
            BoundingBox(Gf.Vec3f(center[0], center[1], min_point[2]), 
                       Gf.Vec3f(max_point[0], max_point[1], center[2])),  # 110
            BoundingBox(Gf.Vec3f(min_point[0], center[1], min_point[2]), 
                       Gf.Vec3f(center[0], max_point[1], center[2])),  # 010
            BoundingBox(Gf.Vec3f(min_point[0], min_point[1], center[2]), 
                       Gf.Vec3f(center[0], center[1], max_point[2])),  # 001
            BoundingBox(Gf.Vec3f(center[0], min_point[1], center[2]), 
                       Gf.Vec3f(max_point[0], center[1], max_point[2])),  # 101
            BoundingBox(Gf.Vec3f(center[0], center[1], center[2]), max_point),  # 111
            BoundingBox(Gf.Vec3f(min_point[0], center[1], center[2]), 
                       Gf.Vec3f(center[0], max_point[1], max_point[2]))   # 011
        ]
        
        for i, child_box in enumerate(child_boxes):
            self.children[i] = OctreeNode(child_box, self.max_depth, self.min_area_threshold)
            self.children[i].depth = current_depth + 1
            self.children[i].subdivide(current_depth + 1)
    
    def add_lod_tile(self, lod_tile: LODTile):
        """添加LOD瓦片到节点"""
        if self.bounding_box.intersects(lod_tile.bounding_box):
            self.lod_tiles.append(lod_tile)
            
            # 递归添加到子节点
            for child in self.children:
                if child:
                    child.add_lod_tile(lod_tile)

class LODScheduler:
    """LOD调度器 - 支持基于几何误差和SSE的LOD选择"""

    def __init__(self, stage: Usd.Stage, camera_path: str = "/World/Camera", centralized_config=None):
        self.stage = stage
        self.camera_path = camera_path
        self.octree: Optional[OctreeNode] = None
        self.lod_tiles: Dict[str, LODTile] = {}

        # 使用中心化配置或默认配置
        if centralized_config:
            self.centralized_config = centralized_config
            # 从中心化配置构建LOD名称到几何误差的映射
            self.lod_geometric_errors = self._build_lod_name_to_error_mapping(centralized_config)
        else:
            from lod_config import get_default_lod_config
            self.centralized_config = get_default_lod_config()
            # 几何误差配置（世界单位，米）- 向后兼容
            self.lod_geometric_errors = {
                "High": 1.0,    # 高质量LOD：1.0米几何误差
                "Medium": 4.0,  # 中质量LOD：4.0米几何误差
                "Low": 8.0,     # 低质量LOD：8.0米几何误差
                "VeryLow": 16.0  # 极低质量LOD：16.0米几何误差
            }

        # SSE配置（从中心化配置获取或使用默认值）
        self.maximum_screen_space_error = self.centralized_config.maximum_screen_space_error
        self.screen_width = self.centralized_config.screen_width
        self.screen_height = self.centralized_config.screen_height
        self.horizontal_fov = self.centralized_config.horizontal_fov

        # 传统LOD配置（向后兼容，移除distance_threshold）
        self.lod_config = {
            LODLevel.HIGH: {"screen_error": 1.0},
            LODLevel.MEDIUM: {"screen_error": 5.0},
            LODLevel.LOW: {"screen_error": 15.0},
            LODLevel.VERY_LOW: {"screen_error": 50.0}
        }

    def _build_lod_name_to_error_mapping(self, centralized_config):
        """从中心化配置构建LOD名称到几何误差的映射"""
        # 获取中心化配置的映射（几何误差 -> LOD名称）
        error_to_lod = centralized_config.get_lod_mapping()

        # 反转映射，构建LOD名称到几何误差的映射
        lod_to_error = {}
        for error, lod_name in error_to_lod.items():
            if lod_name not in lod_to_error:
                lod_to_error[lod_name] = []
            lod_to_error[lod_name].append(error)

        # 为每个LOD级别选择代表性的几何误差值（选择最小值作为代表）
        result = {}
        for lod_name, errors in lod_to_error.items():
            result[lod_name] = min(errors)  # 使用最小的几何误差作为该LOD级别的代表值

        return result
    
    def get_camera_position(self) -> Optional[Gf.Vec3f]:
        """获取相机位置"""
        try:
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                print(f"ERROR: Camera not found at {self.camera_path}")
                return None
            
            xformable = UsdGeom.Xformable(camera)
            if not xformable:
                print("ERROR: Camera is not xformable")
                return None
            
            world_transform = xformable.ComputeLocalToWorldTransform(Usd.TimeCode.Default())
            position = Gf.Vec3f(world_transform[3][0], world_transform[3][1], world_transform[3][2])
            
            return position
        except Exception as e:
            print(f"ERROR: Failed to get camera position: {e}")
            return None
    
    def calculate_sse(self, geometric_error: float, distance_to_camera: float,
                     screen_width: int = None, h_fov: float = None) -> float:
        """
        计算屏幕空间误差(SSE)

        Args:
            geometric_error: 当前LOD的几何误差（世界单位，米）
            distance_to_camera: 相机到对象包围球或包围盒最近点的距离（米）
            screen_width: 视口宽度（像素）
            h_fov: 水平视野角（度）

        Returns:
            float: SSE值，表示投影到屏幕上的误差像素宽度
        """
        if screen_width is None:
            screen_width = self.screen_width
        if h_fov is None:
            h_fov = self.horizontal_fov

        if distance_to_camera <= 0:
            return float('inf')

        # SSE公式：SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))
        sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))

        return sse

    def calculate_distance_to_bounding_sphere(self, camera_pos: Gf.Vec3f, bbox_center: Gf.Vec3f,
                                            bbox_size: Gf.Vec3f) -> float:
        """
        计算相机到包围球的合理距离

        Args:
            camera_pos: 相机位置
            bbox_center: 包围盒中心
            bbox_size: 包围盒尺寸

        Returns:
            float: 用于SSE计算的合理距离
        """
        # 计算包围球半径（使用包围盒的最大尺寸作为直径）
        radius = max(bbox_size[0], bbox_size[1], bbox_size[2]) / 2.0

        # 计算相机到包围球中心的距离
        center_distance = math.sqrt(
            (camera_pos[0] - bbox_center[0])**2 +
            (camera_pos[1] - bbox_center[1])**2 +
            (camera_pos[2] - bbox_center[2])**2
        )

        # 混合距离计算策略：
        # 使用中心距离，但设置合理的最小值
        return max(center_distance, radius * 0.2)

    def calculate_screen_error(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f,
                             fov: float = 60.0, screen_height: int = 1080) -> float:
        """计算屏幕误差（传统方法，保持向后兼容）"""
        # 计算边界框到相机的距离
        center = bounding_box.center
        distance = math.sqrt(sum((center[i] - camera_position[i])**2 for i in range(3)))

        if distance <= 0:
            return float('inf')

        # 计算边界框在屏幕上的投影大小
        # 使用简化的投影计算
        fov_rad = math.radians(fov)
        projected_size = (bounding_box.size[0] + bounding_box.size[1]) / (2 * distance * math.tan(fov_rad / 2))
        screen_pixels = projected_size * screen_height

        return screen_pixels
    
    def calculate_distance_to_camera(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f) -> float:
        """计算边界框到相机的距离"""
        center = bounding_box.center
        return math.sqrt(sum((center[i] - camera_position[i])**2 for i in range(3)))
    
    def is_in_frustum(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f, 
                     fov: float = 60.0, near_plane: float = 0.1, far_plane: float = 1000.0) -> bool:
        """简化的视锥体剔除"""
        distance = self.calculate_distance_to_camera(bounding_box, camera_position)
        
        # 检查距离范围
        if distance < near_plane or distance > far_plane:
            return False
        
        # 简化的视锥体检查（这里可以扩展为更精确的检查）
        return True
    
    def calculate_lod_distance_ranges(self, lod_configs: Dict[str, float] = None,
                                    maximum_screen_space_error: float = None,
                                    screen_width: int = None, h_fov: float = None) -> Tuple[Dict[str, Tuple[float, float]], Dict[str, float]]:
        """
        计算每个LOD对应的距离范围，基于SSE选择策略

        Args:
            lod_configs: LOD配置字典
            maximum_screen_space_error: 最大可接受屏幕误差阈值
            screen_width: 视口宽度
            h_fov: 水平视野角

        Returns:
            tuple: (每个LOD的距离范围, 每个LOD的阈值距离)
        """
        if lod_configs is None:
            lod_configs = self.lod_geometric_errors
        if maximum_screen_space_error is None:
            maximum_screen_space_error = self.maximum_screen_space_error
        if screen_width is None:
            screen_width = self.screen_width
        if h_fov is None:
            h_fov = self.horizontal_fov

        # 计算每个LOD在给定SSE阈值下的临界距离
        # 公式：distance = (geometric_error × screen_width) / (2 × SSE × tan(h_fov/2))
        tan_half_fov = math.tan(math.radians(h_fov / 2))

        lod_threshold_distances = {}
        for lod_name, geometric_error in lod_configs.items():
            threshold_distance = (geometric_error * screen_width) / (2 * maximum_screen_space_error * tan_half_fov)
            lod_threshold_distances[lod_name] = threshold_distance

        # 根据SSE选择策略构建距离范围
        distance_ranges = {}

        very_low_threshold = lod_threshold_distances.get("VeryLow", float('inf'))

        # Low LOD的临界距离：超过此距离，Low LOD就能满足阈值，应该选择Low LOD
        low_threshold = lod_threshold_distances.get("Low", float('inf'))

        # Medium LOD的临界距离：超过此距离，Medium LOD就能满足阈值
        medium_threshold = lod_threshold_distances.get("Medium", float('inf'))

        # High LOD的临界距离：超过此距离，High LOD就能满足阈值
        high_threshold = lod_threshold_distances.get("High", float('inf'))

        # 构建距离范围：
        # - 远距离（>Low阈值）：使用Low LOD（性能优先）
        # - 中距离（Medium阈值到Low阈值）：使用Medium LOD
        # - 近距离（0到Medium阈值）：使用High LOD（质量优先）

        distance_ranges["High"] = (0, medium_threshold)
        distance_ranges["Medium"] = (medium_threshold, low_threshold)
        distance_ranges["Low"] = (low_threshold, very_low_threshold)
        distance_ranges["VeryLow"] = (very_low_threshold, float('inf'))

        return distance_ranges, lod_threshold_distances

    def select_lod_by_sse_and_distance(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f,
                                     verbose: bool = True) -> Tuple[str, Dict[str, any]]:
        """
        基于SSE和距离范围选择LOD

        Args:
            bounding_box: 包围盒
            camera_position: 相机位置
            verbose: 是否输出详细信息

        Returns:
            tuple: (selected_lod, lod_info)
        """
        # 计算到包围球表面的距离
        center = bounding_box.center
        bbox_size = bounding_box.size
        distance_to_camera = self.calculate_distance_to_bounding_sphere(camera_position, center, bbox_size)

        # 计算距离范围
        distance_ranges, lod_max_distances = self.calculate_lod_distance_ranges()

        if verbose:
            print(f"距离范围映射 (SSE阈值={self.maximum_screen_space_error}px):")
            for lod_name in ["High", "Medium", "Low", "VeryLow"]:
                if lod_name in distance_ranges:
                    min_dist, max_dist = distance_ranges[lod_name]
                    max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"
                    print(f"  {lod_name}: {min_dist:.1f}m - {max_dist_str}")
            print(f"当前距离: {distance_to_camera:.1f}m")

        # 根据距离选择LOD
        selected_lod = "VeryLow"  # 默认
        for lod_name, (min_dist, max_dist) in distance_ranges.items():
            if min_dist <= distance_to_camera < max_dist:
                selected_lod = lod_name
                break

        # 计算各LOD的SSE信息（用于显示）
        lod_sse_info = {}
        for lod_name, geometric_error in self.lod_geometric_errors.items():
            if lod_name in ["High", "Medium", "Low", "VeryLow"]:  # 只处理主要的LOD级别
                sse = self.calculate_sse(geometric_error, distance_to_camera)
                lod_sse_info[lod_name] = {
                    'geometric_error': geometric_error,
                    'sse': sse,
                    'max_distance': lod_max_distances.get(lod_name, 0),
                    'distance_range': distance_ranges.get(lod_name, (0, 0)),
                    'acceptable': sse <= self.maximum_screen_space_error
                }

        if verbose:
            print(f"选择结果: {selected_lod} LOD")
            print(f"各LOD的SSE:")
            for lod_name in ["High", "Medium", "Low", "VeryLow"]:
                if lod_name in lod_sse_info:
                    info = lod_sse_info[lod_name]
                    status = "✅" if info['acceptable'] else "❌"
                    print(f"  {lod_name}: SSE={info['sse']:.2f}px, 最大距离={info['max_distance']:.1f}m {status}")

        return selected_lod, lod_sse_info

    def determine_lod_level(self, screen_error: float) -> LODLevel:
        """根据屏幕误差确定LOD级别（传统方法，保持向后兼容）"""
        for level in [LODLevel.HIGH, LODLevel.MEDIUM, LODLevel.LOW, LODLevel.VERY_LOW]:
            config = self.lod_config[level]
            if screen_error <= config["screen_error"]:
                return level

        return LODLevel.VERY_LOW
    
    def build_octree_from_tileset(self, tileset_path: str, base_path: str = None):
        """从tileset.json文件构建八叉树"""
        print(f"Building octree from tileset: {tileset_path}")

        try:
            with open(tileset_path, 'r', encoding='utf-8') as f:
                tileset_data = json.load(f)

            # 获取基础路径
            if base_path is None:
                base_path = os.path.dirname(tileset_path)

            # 从tileset根节点构建八叉树
            root_tile = tileset_data.get('root')
            if not root_tile:
                print("ERROR: No root tile found in tileset")
                return

            # 解析根节点的边界框
            root_bounds = self._parse_tileset_bounding_box(root_tile.get('boundingVolume', {}))
            if not root_bounds:
                print("ERROR: Failed to parse root bounding box")
                return

            print(f"Tileset root bounds: {root_bounds.min_point} to {root_bounds.max_point}")

            # 创建八叉树根节点
            self.octree = OctreeNode(root_bounds, max_depth=8, min_area_threshold=100.0)

            # 递归构建tileset层次结构
            self._build_tileset_hierarchy(root_tile, self.octree, base_path)

            print(f"Octree built from tileset with {len(self.lod_tiles)} LOD tiles")

        except Exception as e:
            print(f"ERROR: Failed to build octree from tileset: {e}")
            import traceback
            traceback.print_exc()
    
    def _calculate_scene_bounds(self) -> Optional[BoundingBox]:
        """计算场景边界框"""
        min_point = Gf.Vec3f(float('inf'), float('inf'), float('inf'))
        max_point = Gf.Vec3f(float('-inf'), float('-inf'), float('-inf'))
        
        found_bounds = False
        
        for prim in self.stage.Traverse():
            # 查找有边界信息的prim
            attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
            attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
            
            if attr_min and attr_max:
                try:
                    min_bounds = Gf.Vec3f(*attr_min.Get())
                    max_bounds = Gf.Vec3f(*attr_max.Get())
                    
                    min_point = Gf.Vec3f(
                        min(min_point[0], min_bounds[0]),
                        min(min_point[1], min_bounds[1]),
                        min(min_point[2], min_bounds[2])
                    )
                    max_point = Gf.Vec3f(
                        max(max_point[0], max_bounds[0]),
                        max(max_point[1], max_bounds[1]),
                        max(max_point[2], max_bounds[2])
                    )
                    found_bounds = True
                except Exception as e:
                    print(f"Warning: Failed to read bounds for {prim.GetPath()}: {e}")
        
        if not found_bounds:
            print("Warning: No bounds found in stage, using default bounds")
            min_point = Gf.Vec3f(-1000, -1000, -1000)
            max_point = Gf.Vec3f(1000, 1000, 1000)
        
        return BoundingBox(min_point, max_point)

    def _parse_tileset_bounding_box(self, bounding_volume: dict) -> Optional[BoundingBox]:
        """解析tileset中的边界框"""
        if 'box' in bounding_volume:
            # 3D Tiles格式的box: [center_x, center_y, center_z, x_axis_x, x_axis_y, x_axis_z, y_axis_x, y_axis_y, y_axis_z, z_axis_x, z_axis_y, z_axis_z]
            box = bounding_volume['box']
            if len(box) >= 12:
                center = Gf.Vec3f(box[0], box[1], box[2])

                # 计算半轴长度
                x_axis = Gf.Vec3f(box[3], box[4], box[5])
                y_axis = Gf.Vec3f(box[6], box[7], box[8])
                z_axis = Gf.Vec3f(box[9], box[10], box[11])

                # 计算边界框的最小和最大点
                x_extent = abs(x_axis[0]) + abs(y_axis[0]) + abs(z_axis[0])
                y_extent = abs(x_axis[1]) + abs(y_axis[1]) + abs(z_axis[1])
                z_extent = abs(x_axis[2]) + abs(y_axis[2]) + abs(z_axis[2])

                min_point = Gf.Vec3f(
                    center[0] - x_extent,
                    center[1] - y_extent,
                    center[2] - z_extent
                )
                max_point = Gf.Vec3f(
                    center[0] + x_extent,
                    center[1] + y_extent,
                    center[2] + z_extent
                )

                return BoundingBox(min_point, max_point)

        elif 'region' in bounding_volume:
            # 地理区域格式
            region = bounding_volume['region']
            if len(region) >= 6:
                # [west, south, east, north, minimum_height, maximum_height]
                min_point = Gf.Vec3f(region[0], region[1], region[4])
                max_point = Gf.Vec3f(region[2], region[3], region[5])
                return BoundingBox(min_point, max_point)

        elif 'sphere' in bounding_volume:
            # 球形边界
            sphere = bounding_volume['sphere']
            if len(sphere) >= 4:
                center = Gf.Vec3f(sphere[0], sphere[1], sphere[2])
                radius = sphere[3]
                min_point = Gf.Vec3f(center[0] - radius, center[1] - radius, center[2] - radius)
                max_point = Gf.Vec3f(center[0] + radius, center[1] + radius, center[2] + radius)
                return BoundingBox(min_point, max_point)

        return None

    def _build_tileset_hierarchy(self, tile_data: dict, octree_node: OctreeNode, base_path: str, depth: int = 0):
        """递归构建tileset层次结构"""
        try:
            # 解析当前瓦片的边界框
            bounding_box = self._parse_tileset_bounding_box(tile_data.get('boundingVolume', {}))
            if not bounding_box:
                print(f"Warning: Failed to parse bounding box for tile at depth {depth}")
                return

            # 获取几何误差
            geometric_error = tile_data.get('geometricError', 1.0)

            # 获取内容URI
            content = tile_data.get('content', {})
            uri = content.get('uri', '')

            # 根据几何误差确定LOD级别（使用中心化配置）
            lod_name = self.centralized_config.get_lod_level_from_geometric_error(geometric_error)
            lod_level_map = {"High": LODLevel.HIGH, "Medium": LODLevel.MEDIUM, "Low": LODLevel.LOW, "VeryLow": LODLevel.VERY_LOW}
            lod_level = lod_level_map.get(lod_name, LODLevel.VERY_LOW)

            # 构建完整的文件路径
            if uri:
                if os.path.isabs(uri):
                    usdz_path = uri
                else:
                    usdz_path = os.path.join(base_path, uri).replace('\\', '/')
            else:
                usdz_path = f"tile_depth_{depth}.usdz"

            # 创建LOD瓦片
            tile_id = f"tileset_tile_{depth}_{len(self.lod_tiles)}"
            # 计算同一depth层级内的索引
            depth_tiles_count = sum(1 for t in self.lod_tiles.values() if hasattr(t, 'depth') and t.depth == depth)
            lod_tile = LODTile(
                id=tile_id,
                bounding_box=bounding_box,
                lod_level=lod_level,
                usdz_path=usdz_path,
                screen_error=geometric_error,
                geometric_error=geometric_error,
                tile_index=depth_tiles_count,  # 使用同一depth层级内的索引
                depth=depth  # 添加depth信息
            )

            # 添加到调度器
            self.lod_tiles[tile_id] = lod_tile
            octree_node.add_lod_tile(lod_tile)

            print(f"Added tileset tile: {tile_id}, LOD: {lod_name}, GeometricError: {geometric_error}, URI: {uri}")

            # 递归处理子瓦片
            children = tile_data.get('children', [])
            for child_tile in children:
                self._build_tileset_hierarchy(child_tile, octree_node, base_path, depth + 1)

        except Exception as e:
            print(f"Error building tileset hierarchy at depth {depth}: {e}")
            import traceback
            traceback.print_exc()
    
    def update_lod_visibility(self, fov: float = 60.0, screen_height: int = 1080, use_sse_method: bool = True):
        """更新LOD可见性"""
        if not self.octree:
            print("ERROR: Octree not built. Call build_octree_from_stage() first.")
            return

        camera_position = self.get_camera_position()
        if not camera_position:
            print("ERROR: Failed to get camera position")
            return

        print(f"Updating LOD visibility for camera at {camera_position}")

        if use_sse_method:
            # 使用新的SSE方法
            self._update_node_visibility_sse(self.octree, camera_position, fov, screen_height)
        else:
            # 使用传统方法（向后兼容）
            self._update_node_visibility(self.octree, camera_position, fov, screen_height)

    def update_lod_visibility_by_sse(self, region_bounds: BoundingBox = None, verbose: bool = True):
        """使用SSE策略更新LOD可见性（兼容单区域场景）"""
        camera_position = self.get_camera_position()
        if not camera_position:
            if verbose:
                print("ERROR: Failed to get camera position")
            return None, None, None

        if verbose:
            print(f"\n=== Updating LOD Visibility by SSE Strategy ===")
            print(f"Camera position: {camera_position}")

        # 如果没有提供区域边界，尝试从八叉树或stage中获取
        if region_bounds is None:
            if self.octree:
                region_bounds = self.octree.bounding_box
            else:
                # 从stage中计算场景边界
                region_bounds = self._calculate_scene_bounds()
                if not region_bounds:
                    if verbose:
                        print("ERROR: Failed to calculate scene bounds")
                    return None, None, None

        # 使用SSE方法选择LOD
        selected_lod, lod_sse_info = self.select_lod_by_sse_and_distance(
            region_bounds, camera_position, verbose
        )

        # 计算中心距离用于显示
        center = region_bounds.center
        center_distance = math.sqrt(
            (camera_position[0] - center[0])**2 +
            (camera_position[1] - center[1])**2 +
            (camera_position[2] - center[2])**2
        )

        if verbose:
            print(f"Distance to region center: {center_distance:.1f}m")
            print(f"Selected LOD level: {selected_lod}")

        # 更新所有LOD级别的可见性
        lod_levels = ["High", "Medium", "Low", "VeryLow"]
        for lod_name in lod_levels:
            lod_path = f"/World/SingleRegion/LOD_{lod_name}"
            lod_prim = self.stage.GetPrimAtPath(lod_path)

            if lod_prim:
                # 使用UsdGeom.Imageable设置可见性
                imageable = UsdGeom.Imageable(lod_prim)
                vis_attr = imageable.GetVisibilityAttr()

                # 设置可见性：只有目标LOD可见，其他都隐藏
                visible = (lod_name == selected_lod)
                vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)

                if verbose:
                    print(f"  LOD_{lod_name}: {'Visible' if visible else 'Hidden'}")
            else:
                if verbose:
                    print(f"  LOD_{lod_name}: Not found")

        # 返回用于日志/状态的结果
        representative_sse = lod_sse_info.get(selected_lod, {}).get('sse', 0.0)
        return selected_lod, center_distance, representative_sse
    
    def _update_node_visibility_sse(self, node: OctreeNode, camera_position: Gf.Vec3f,
                                  fov: float, screen_height: int):
        """使用SSE方法递归更新节点可见性"""
        if not node:
            return

        # 检查节点是否在视锥体内
        if not self.is_in_frustum(node.bounding_box, camera_position, fov):
            print(f"Node {node.bounding_box} not in frustum, hiding...")
            # 隐藏节点中的所有LOD瓦片
            self._hide_lod_tiles(node.lod_tiles)
            return

        # print(f"Node {node.bounding_box} in frustum, checking LOD...")

        # 使用SSE方法选择LOD
        selected_lod, lod_sse_info = self.select_lod_by_sse_and_distance(
            node.bounding_box, camera_position, verbose=False
        )

        # print(f"Selected LOD level: {selected_lod}")

        # 检查是否需要细分
        # 如果选择的是High LOD且有子节点，则继续细分
        should_subdivide = (node.children[0] is not None and
                           node.bounding_box.area > node.min_area_threshold and
                           selected_lod == "High")

        if should_subdivide:
            # print(f"Subdividing node {node.bounding_box}...")
            # print(f"Has children: {[child is not None for child in node.children]}")
            # 递归处理子节点
            for child in node.children:
                if child:
                    self._update_node_visibility_sse(child, camera_position, fov, screen_height)
        else:
            # 显示当前节点对应的LOD级别
            # 将字符串LOD级别转换为枚举
            lod_level_map = {
                "High": LODLevel.HIGH,
                "Medium": LODLevel.MEDIUM,
                "Low": LODLevel.LOW,
                "VeryLow": LODLevel.VERY_LOW
            }
            lod_level = lod_level_map.get(selected_lod, LODLevel.VERY_LOW)
            self._show_lod_tiles_for_level(node.lod_tiles, lod_level)

    def _update_node_visibility(self, node: OctreeNode, camera_position: Gf.Vec3f,
                              fov: float, screen_height: int):
        """递归更新节点可见性（传统方法）"""
        if not node:
            return

        # 检查节点是否在视锥体内
        if not self.is_in_frustum(node.bounding_box, camera_position, fov):
            # 隐藏节点中的所有LOD瓦片
            self._hide_lod_tiles(node.lod_tiles)
            return

        # 计算屏幕误差
        screen_error = self.calculate_screen_error(node.bounding_box, camera_position, fov, screen_height)

        # 确定LOD级别
        lod_level = self.determine_lod_level(screen_error)

        # 检查是否需要细分
        should_subdivide = (node.children[0] is not None and
                           node.bounding_box.area > node.min_area_threshold and
                           screen_error > self.lod_config[LODLevel.HIGH]["screen_error"])

        if should_subdivide:
            # 递归处理子节点
            for child in node.children:
                if child:
                    self._update_node_visibility(child, camera_position, fov, screen_height)
        else:
            # 显示当前节点对应的LOD级别
            self._show_lod_tiles_for_level(node.lod_tiles, lod_level)
    
    def _hide_lod_tiles(self, lod_tiles: List[LODTile]):
        """隐藏LOD瓦片"""
        for tile in lod_tiles:
            # 查找对应的prim并隐藏
            prim = self.stage.GetPrimAtPath(tile.id)
            if prim:
                parent2 = prim.GetParent().GetParent()
                if parent2:
                    vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
                    vis_attr.Set(UsdGeom.Tokens.invisible)
    

    def _show_lod_tiles_for_level(self, lod_tiles: List[LODTile], target_level: LODLevel):
        """显示指定级别的LOD瓦片（先按depth排序，再按同一depth内的tile_index排序）"""

        # 先按depth排序，再按同一depth内的tile_index排序
        # sorted_tiles = sorted(lod_tiles, key=lambda t: (t.depth, t.tile_index))

        for tile in lod_tiles:
            prim = self.stage.GetPrimAtPath(tile.id)
            if prim:
                parent2 = prim.GetParent().GetParent()
                if parent2:
                    vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()

                    if tile.lod_level == target_level:
                        vis_attr.Set(UsdGeom.Tokens.inherited)
                    else:
                        vis_attr.Set(UsdGeom.Tokens.invisible)

class TilesetLODManager:
    """
    分层Tileset LOD管理器
    专门处理分层tileset结构的LOD调度，与单一区域LOD调度区分开来
    """

    def __init__(self, stage: Usd.Stage, camera_path: str = "/World/Camera",
                 lod_scheduler: LODScheduler = None, config=None):
        """
        初始化TilesetLODManager

        Args:
            stage: USD Stage
            camera_path: 相机路径
            lod_scheduler: LOD调度器实例（用于SSE计算）
            config: 配置对象
        """
        self.stage = stage
        self.camera_path = camera_path
        self.lod_scheduler = lod_scheduler
        self.config = config

        # 缓存数据
        self._cached_tiles = None
        self._cached_region_bounds = None

        # 自动更新相关
        self._auto_update_active = False
        self._auto_update_subscription = None

    def get_tileset_region_bounds(self) -> Optional[BoundingBox]:
        """从已加载的stage中获取tileset区域边界"""
        if self._cached_region_bounds:
            return self._cached_region_bounds

        try:
            # 查找TilesetRegion
            region_path = "/World/TilesetRegion"
            region_prim = self.stage.GetPrimAtPath(region_path)

            if not region_prim or not region_prim.IsValid():
                print(f"ERROR: TilesetRegion not found at {region_path}")
                return None

            # 递归查找第一个有边界信息的tile
            def find_bounds_in_hierarchy(prim):
                """递归查找有边界信息的tile"""
                # 检查当前prim是否有边界信息
                min_bounds_attr = prim.GetAttribute("tileset:minBounds")
                max_bounds_attr = prim.GetAttribute("tileset:maxBounds")

                if min_bounds_attr and max_bounds_attr:
                    min_point = Gf.Vec3f(*min_bounds_attr.Get())
                    max_point = Gf.Vec3f(*max_bounds_attr.Get())
                    return BoundingBox(min_point, max_point)

                # 递归检查子节点
                for child in prim.GetChildren():
                    bounds = find_bounds_in_hierarchy(child)
                    if bounds:
                        return bounds

                return None

            # 从根tile开始查找边界
            bounds = find_bounds_in_hierarchy(region_prim)

            if bounds:
                self._cached_region_bounds = bounds
                print(f"Found tileset region bounds: {bounds.min_point} to {bounds.max_point}")
                return bounds
            else:
                print("ERROR: No tile with bounds found in TilesetRegion hierarchy")
                return None

        except Exception as e:
            print(f"ERROR: Failed to get tileset region bounds: {e}")
            return None

    def collect_all_tiles(self) -> List[Dict]:
        """从stage中收集所有的tile节点信息"""
        if self._cached_tiles:
            return self._cached_tiles

        try:
            # 查找TilesetRegion
            region_path = "/World/TilesetRegion"
            region_prim = self.stage.GetPrimAtPath(region_path)

            if not region_prim or not region_prim.IsValid():
                print(f"ERROR: TilesetRegion not found at {region_path}")
                return []

            tiles = []

            def collect_tiles_recursive(prim, path=""):
                """递归收集tile信息"""
                prim_name = prim.GetName()
                current_path = f"{path}/{prim_name}" if path else prim_name

                # 检查是否是tile节点
                if prim_name.startswith("Tile_"):
                    tile_info = {
                        'path': prim.GetPath().pathString,
                        'name': prim_name,
                        'prim': prim,
                        'content_nodes': [],
                        'depth': None,
                        'geometric_error': None,
                        'has_content': False,
                        'bounds': None
                    }

                    # 获取tile属性
                    depth_attr = prim.GetAttribute("tileset:depth")
                    if depth_attr:
                        tile_info['depth'] = depth_attr.Get()

                    geom_error_attr = prim.GetAttribute("tileset:geometricError")
                    if geom_error_attr:
                        tile_info['geometric_error'] = geom_error_attr.Get()

                    has_content_attr = prim.GetAttribute("tileset:hasContent")
                    if has_content_attr:
                        tile_info['has_content'] = has_content_attr.Get()

                    # 获取边界
                    min_bounds_attr = prim.GetAttribute("tileset:minBounds")
                    max_bounds_attr = prim.GetAttribute("tileset:maxBounds")
                    if min_bounds_attr and max_bounds_attr:
                        min_point = Gf.Vec3f(*min_bounds_attr.Get())
                        max_point = Gf.Vec3f(*max_bounds_attr.Get())
                        tile_info['bounds'] = BoundingBox(min_point, max_point)

                    # 查找内容节点
                    for child in prim.GetChildren():
                        child_name = child.GetName()
                        if child_name.startswith("Content_LOD_"):
                            content_info = {
                                'path': child.GetPath().pathString,
                                'name': child_name,
                                'prim': child,
                                'lod_level': None,
                                'uri': None
                            }

                            # 获取LOD级别
                            lod_level_attr = child.GetAttribute("tileset:lodLevel")
                            if lod_level_attr:
                                content_info['lod_level'] = lod_level_attr.Get()

                            # 获取URI
                            uri_attr = child.GetAttribute("tileset:uri")
                            if uri_attr:
                                content_info['uri'] = uri_attr.Get()

                            tile_info['content_nodes'].append(content_info)

                    tiles.append(tile_info)

                # 递归处理子节点
                for child in prim.GetChildren():
                    collect_tiles_recursive(child, current_path)

            # 从TilesetRegion开始收集
            collect_tiles_recursive(region_prim)

            self._cached_tiles = tiles
            print(f"Collected {len(tiles)} tiles from stage hierarchy")
            return tiles

        except Exception as e:
            print(f"ERROR: Failed to collect tiles from stage: {e}")
            return []

    def update_tileset_lod_visibility(self, verbose: bool = True) -> Tuple[Optional[str], Optional[float], Optional[float]]:
        """
        简化的LOD可见性更新
        核心逻辑：当相机在LOD 20 Tile包围盒内时，强制加载LOD 20而不是父级LOD

        Returns:
            tuple: (selected_lod_name, center_distance, representative_sse)
        """
        if verbose:
            print("\n=== Simplified LOD Update: Camera Inside LOD 20 Tiles Priority ===")

        # 导入工具类
        from tileset_utils import FrustumCuller

        try:
            # 获取相机
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                if verbose:
                    print("ERROR: Camera not found")
                return None, None, None

            # 获取相机位置
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())

            # 获取区域边界
            region_bounds = self.get_tileset_region_bounds()
            if not region_bounds:
                if verbose:
                    print("ERROR: Could not find tileset region bounds")
                return None, None, None

            # 收集所有tiles
            all_tiles = self.collect_all_tiles()
            if not all_tiles:
                if verbose:
                    print("ERROR: No tiles found in stage")
                return None, None, None

            # 计算到区域中心的距离
            center_distance = math.sqrt(
                (camera_position[0] - region_bounds.center[0])**2 +
                (camera_position[1] - region_bounds.center[1])**2 +
                (camera_position[2] - region_bounds.center[2])**2
            )

            # 检查LOD调度器
            if not self.lod_scheduler:
                if verbose:
                    print("ERROR: LOD scheduler not provided")
                return None, None, None

            if verbose:
                print(f"📍 Camera position: {camera_position}")
                print(f"📏 Distance to region center: {center_distance:.1f}m")

            # === 简化的LOD处理策略 ===
            # 第一步：找出所有LOD 20的Tile
            lod_20_tiles = []
            for tile in all_tiles:
                if not tile['content_nodes']:
                    continue

                # 检查该Tile是否有LOD 20的内容
                has_lod_20 = any(content.get('lod_level', 14) == 20 for content in tile['content_nodes'])
                if has_lod_20:
                    lod_20_tiles.append(tile)

            if verbose:
                print(f"🎯 Found {len(lod_20_tiles)} tiles with LOD 20 content")

            # 第二步：检测相机是否在任何LOD 20 Tile内
            camera_inside_lod20_tiles = []
            for tile in lod_20_tiles:
                tile_bounds = tile.get('bounds')
                if tile_bounds and self._is_camera_inside_tile(camera_position, tile_bounds):
                    camera_inside_lod20_tiles.append(tile)
                    if verbose:
                        print(f"📍 Camera is inside LOD 20 tile: {tile['name']}")

            # 第三步：为每个Tile选择LOD并应用特殊规则
            visible_count = 0
            hidden_count = 0
            lod_distribution = {"High": 0, "Medium": 0, "Low": 0, "VeryLow": 0}

            for tile in all_tiles:
                if not tile['content_nodes']:
                    continue

                # 为每个Tile选择LOD级别
                tile_bounds = tile.get('bounds')
                if tile_bounds:
                    tile_selected_lod, _ = self.lod_scheduler.select_lod_by_sse_and_distance(
                        tile_bounds, camera_position, verbose=False
                    )
                    tile_distance = math.sqrt(
                        (camera_position[0] - tile_bounds.center[0])**2 +
                        (camera_position[1] - tile_bounds.center[1])**2 +
                        (camera_position[2] - tile_bounds.center[2])**2
                    )
                else:
                    tile_selected_lod, _ = self.lod_scheduler.select_lod_by_sse_and_distance(
                        region_bounds, camera_position, verbose=False
                    )
                    tile_distance = center_distance

                # 将LOD名称转换为数值级别
                lod_name_to_level = {"High": 20, "Medium": 18, "Low": 16, "VeryLow": 14}
                tile_target_lod_level = lod_name_to_level.get(tile_selected_lod, 14)

                # 特殊规则：如果相机在任何LOD 20 Tile内，且当前Tile有LOD 20内容，强制使用LOD 20
                force_lod_20 = (camera_inside_lod20_tiles and
                               tile in lod_20_tiles and
                               any(content.get('lod_level', 14) == 20 for content in tile['content_nodes']))

                # 处理该Tile的所有内容
                for content in tile['content_nodes']:
                    content_lod_level = content.get('lod_level', 14)

                    # 决定可见性
                    if force_lod_20:
                        # 强制LOD 20：只显示LOD 20内容
                        visible = (content_lod_level == 20)
                        if visible and verbose:
                            print(f"🎯 Forced LOD 20: {content['name']} in {tile['name']}")
                    else:
                        # 正常LOD匹配
                        visible = (content_lod_level == tile_target_lod_level)

                    # 应用可见性
                    imageable = UsdGeom.Imageable(content['prim'])
                    if visible:
                        imageable.MakeVisible()
                        visible_count += 1
                        lod_distribution[tile_selected_lod] += 1
                    else:
                        imageable.MakeInvisible()
                        hidden_count += 1

            # 简化的统计和显示
            most_common_lod = max(lod_distribution, key=lod_distribution.get) if any(lod_distribution.values()) else "None"

            if verbose:
                print(f"\n Simplified LOD Update Summary:")
                print(f"  Strategy: Camera Inside LOD 20 Tiles Priority")
                print(f"  LOD 20 Tiles Found: {len(lod_20_tiles)}")
                print(f"  Camera Inside LOD 20 Tiles: {len(camera_inside_lod20_tiles)}")
                if camera_inside_lod20_tiles:
                    print(f"  Camera Inside Tiles: {[tile['name'] for tile in camera_inside_lod20_tiles]}")
                print(f"  LOD Distribution: {dict(lod_distribution)}")
                print(f"  Most Common LOD: {most_common_lod}")
                print(f"  Visible content nodes: {visible_count}")
                print(f"  Hidden content nodes: {hidden_count}")
                print(f"  Camera distance: {center_distance:.1f}m")

            return most_common_lod, center_distance, None

        except Exception as e:
            if verbose:
                print(f"Error updating hierarchical tileset LOD visibility: {e}")
                import traceback
                traceback.print_exc()
            return None, None, None



    def _is_camera_inside_tile(self, camera_position, tile_bounds):
        """
        检测相机是否在Tile包围盒内

        Args:
            camera_position: 相机位置
            tile_bounds: Tile包围盒

        Returns:
            bool: 相机是否在Tile内
        """
        if not tile_bounds:
            return False

        # 检查相机位置是否在包围盒内
        min_pos = tile_bounds.min
        max_pos = tile_bounds.max

        return (min_pos[0] <= camera_position[0] <= max_pos[0] and
                min_pos[1] <= camera_position[1] <= max_pos[1] and
                min_pos[2] <= camera_position[2] <= max_pos[2])



    def start_automatic_lod_switching(self, update_interval: float = 1.0) -> bool:
        """
        启动自动LOD切换

        Args:
            update_interval: 更新间隔（秒）

        Returns:
            bool: 是否启动成功
        """
        if self._auto_update_active:
            print("Automatic LOD switching is already active")
            return True

        try:
            import omni.kit.app
            app = omni.kit.app.get_app()
            stream = app.get_update_event_stream()

            last_ts = time.time()
            accumulator = 0.0
            last_lod_state = {"last_lod": None, "last_distance": None}

            def on_update(_):
                nonlocal last_ts, accumulator, last_lod_state
                if not self._auto_update_active:
                    return

                now = time.time()
                dt = now - last_ts
                last_ts = now
                accumulator += dt

                if accumulator < update_interval:
                    return
                accumulator = 0.0

                try:
                    # 更新LOD并获取指标
                    current_lod, distance, screen_error = self.update_tileset_lod_visibility(verbose=False)
                    if (current_lod and
                        (last_lod_state["last_lod"] != current_lod or
                         abs((last_lod_state["last_distance"] or 0) - distance) > 10.0)):
                        print(f"🔄 Tileset LOD switched to {current_lod} (distance: {distance}, screen: {screen_error})")
                        last_lod_state["last_lod"] = current_lod
                        last_lod_state["last_distance"] = distance
                except Exception as ex:
                    print(f"Error in automatic LOD update: {ex}")

            self._auto_update_subscription = stream.create_subscription_to_pop(
                on_update, name="TilesetLODAutoUpdate"
            )
            self._auto_update_active = True

            print(f"✅ Automatic tileset LOD switching started (interval: {update_interval}s)")
            return True

        except Exception as e:
            print(f"ERROR: Failed to start automatic LOD switching: {e}")
            return False

    def stop_automatic_lod_switching(self) -> bool:
        """
        停止自动LOD切换

        Returns:
            bool: 是否停止成功
        """
        try:
            self._auto_update_active = False

            if self._auto_update_subscription:
                self._auto_update_subscription.unsubscribe()
                self._auto_update_subscription = None

            print("✅ Automatic tileset LOD switching stopped")
            return True

        except Exception as e:
            print(f"ERROR: Failed to stop automatic LOD switching: {e}")
            return False

    def clear_cache(self):
        """清除缓存数据"""
        self._cached_tiles = None
        self._cached_region_bounds = None
        print("Cache cleared")

    def get_status(self) -> Dict:
        """获取当前状态信息"""
        return {
            'auto_update_active': self._auto_update_active,
            'has_cached_tiles': self._cached_tiles is not None,
            'has_cached_bounds': self._cached_region_bounds is not None,
            'camera_path': self.camera_path,
            'has_lod_scheduler': self.lod_scheduler is not None
        }

    def update_tileset_lod_visibility_simple(self, verbose: bool = True) -> Tuple[Optional[str], Optional[float], Optional[float]]:
        """
        简化版本的LOD更新，用于调试卡住问题

        Returns:
            tuple: (selected_lod_name, center_distance, representative_sse)
        """
        if verbose:
            print("\n=== Simple LOD Update (Debug Mode) ===")

        try:
            # 基本检查
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                print("ERROR: Camera not found")
                return None, None, None

            # 获取相机位置
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())

            if verbose:
                print(f"Camera position: {camera_position}")

            # 获取区域边界
            region_bounds = self.get_tileset_region_bounds()
            if not region_bounds:
                print("ERROR: Could not find tileset region bounds")
                return None, None, None

            # 计算距离
            center_distance = math.sqrt(
                (camera_position[0] - region_bounds.center[0])**2 +
                (camera_position[1] - region_bounds.center[1])**2 +
                (camera_position[2] - region_bounds.center[2])**2
            )

            if verbose:
                print(f"Distance to region center: {center_distance:.1f}m")

            # 简单的LOD选择
            if not self.lod_scheduler:
                print("ERROR: LOD scheduler not provided")
                return None, None, None

            selected_lod, lod_info = self.lod_scheduler.select_lod_by_sse_and_distance(
                region_bounds, camera_position, verbose=verbose
            )

            if verbose:
                print(f"Selected LOD: {selected_lod}")

            return selected_lod, center_distance, lod_info.get('sse') if lod_info else None

        except Exception as e:
            if verbose:
                print(f"ERROR in simple LOD update: {e}")
                import traceback
                traceback.print_exc()
            return None, None, None